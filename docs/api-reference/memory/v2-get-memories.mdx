---
title: 'Get Memories (v2)'
openapi: post /v2/memories/
---

The v2 get memories API is powerful and flexible, allowing for more precise memory listing without the need for a search query. It supports complex logical operations (AND, OR) and comparison operators for advanced filtering capabilities. The comparison operators include:
- `in`: Matches any of the values specified
- `gte`: Greater than or equal to
- `lte`: Less than or equal to
- `gt`: Greater than
- `lt`: Less than

<CodeGroup>
```python Code
memories = m.get_all(
    filters={
        "AND": [
            {
                "user_id": "alex"
            },
            {
                "created_at": {"gte": "2024-07-01", "lte": "2024-07-31"}
            }
        ]
    },
    version="v2"
)
```

```json Output
[
{
    "id":"f38b689d-6b24-45b7-bced-17fbb4d8bac7",
    "memory":"Name: Alex. Vegetarian. Allergic to nuts.",
    "user_id":"alex",
    "hash":"62bc074f56d1f909f1b4c2b639f56f6a",
    "metadata":null,
    "created_at":"2024-07-25T23:57:00.108347-07:00",
    "updated_at":"2024-07-25T23:57:00.108367-07:00"
}
]
```
</CodeGroup>
