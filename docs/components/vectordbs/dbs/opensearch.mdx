[OpenSearch](https://opensearch.org/) is an open-source, enterprise-grade search and observability suite that brings order to unstructured data at scale. OpenSearch supports k-NN (k-Nearest Neighbors) and allows you to store and retrieve high-dimensional vector embeddings efficiently.

### Installation

OpenSearch support requires additional dependencies. Install them with:

```bash
pip install opensearch>=2.8.0
```

### Usage

```python
import os
from mem0 import Memory

os.environ["OPENAI_API_KEY"] = "sk-xx"

config = {
    "vector_store": {
        "provider": "opensearch",
        "config": {
            "collection_name": "mem0",
            "host": "localhost",
            "port": 9200,
            "embedding_model_dims": 1536
        }
    }
}

m = Memory.from_config(config)
messages = [
    {"role": "user", "content": "I'm planning to watch a movie tonight. Any recommendations?"},
    {"role": "assistant", "content": "How about a thriller movies? They can be quite engaging."},
    {"role": "user", "content": "I’m not a big fan of thriller movies but I love sci-fi movies."},
    {"role": "assistant", "content": "Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future."}
]
m.add(messages, user_id="alice", metadata={"category": "movies"})
```

### Config

Let's see the available parameters for the `opensearch` config:

| Parameter              | Description                                        | Default Value |
| ---------------------- | -------------------------------------------------- | ------------- |
| `collection_name`      | The name of the index to store the vectors         | `mem0`        |
| `embedding_model_dims` | Dimensions of the embedding model                  | `1536`        |
| `host`                 | The host where the OpenSearch server is running    | `localhost`   |
| `port`                 | The port where the OpenSearch server is running    | `9200`        |
| `api_key`              | API key for authentication                         | `None`        |
| `user`                 | Username for basic authentication                  | `None`        |
| `password`             | Password for basic authentication                  | `None`        |
| `verify_certs`         | Whether to verify SSL certificates                 | `False`        |
| `auto_create_index`    | Whether to automatically create the index          | `True`        |
| `use_ssl`              | Whether to use SSL for connection                  | `False`        |

### Features

- Fast and Efficient Vector Search
- Can be deployed on-premises, in containers, or on cloud platforms like AWS OpenSearch Service.
- Multiple Authentication and Security Methods (Basic Authentication, API Keys, LDAP, SAML, and OpenID Connect)
- Automatic index creation with optimized mappings for vector search
- Memory Optimization through Disk-Based Vector Search and Quantization
- Real-Time Analytics and Observability
