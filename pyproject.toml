[tool.poetry]
name = "mem0ai"
version = "0.1.97"
description = "Long-term memory for AI Agents"
authors = ["Mem0 <<EMAIL>>"]
exclude = [
    "db",
    "configs",
    "notebooks",
    "embedchain",
    "evaluation",
    "mem0-ts",
    "examples",
    "vercel-ai-sdk",
    "docs",
]
packages = [
    { include = "mem0" },
]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.9,<4.0"
qdrant-client = "^1.9.1"
pydantic = "^2.7.3"
openai = "^1.33.0"
posthog = "^3.5.0"
pytz = "^2024.1"
sqlalchemy = "^2.0.31"
langchain-neo4j = "^0.4.0"
neo4j = "^5.23.1"
rank-bm25 = "^0.2.2"

[tool.poetry.extras]
graph = ["langchain-neo4j", "neo4j", "rank-bm25"]

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-mock = "^3.14.0"
pytest-asyncio = "^0.23.7"

[tool.poetry.group.dev.dependencies]
ruff = "^0.6.5"
isort = "^5.13.2"
pytest = "^8.2.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 120
exclude = ["embedchain/"]
